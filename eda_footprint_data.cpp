#include "eda_footprint_data.h"
#include "eda_board_data.h"
#include "eda_brd_field_data.h"
#include "eda_pad_data.h"
#include "eda_brd_shape_data.h"
#include "eda_brd_text_data.h"
#include "eda_zone_data.h"
#include <QDebug>
#include <algorithm>



// Constructor
EDA_FOOTPRINT_DATA::EDA_FOOTPRINT_DATA(EDA_BOARD_DATA* parent)
    : EDA_BOARD_OBJECT_CONTAINER(static_cast<EDA_BOARD_OBJECT_DATA*>(parent), QtKicadType::PcbFootprint),
      m_pos(0, 0),
      m_orient(0.0),
      m_attributes(0),
      m_fpStatus(FP_PADS_are_LOCKED),
      m_arflag(0),
      m_fileFormatVersionAtLoad(0),
      m_link(),
      m_lastEditTime(0),
      m_zoneConnection(QtZoneConnection::INHERIT),
      m_componentClass(nullptr),
      m_boundingBoxCacheTimeStamp(0),
      m_textExcludedBBoxCacheTimeStamp(0),
      m_hullCacheTimeStamp(0),
      m_initialComments(nullptr),
      m_embedFonts(false)
{
    setLayer(QtPcbLayerId::FCu);
    addMandatoryFields();
    m_3DDrawings.clear();
}

// Copy constructor
EDA_FOOTPRINT_DATA::EDA_FOOTPRINT_DATA(const EDA_FOOTPRINT_DATA& aFootprint)
    : EDA_BOARD_OBJECT_CONTAINER(aFootprint),
      m_pos(aFootprint.m_pos),
      m_fpid(aFootprint.m_fpid),
      m_attributes(aFootprint.m_attributes),
      m_fpStatus(aFootprint.m_fpStatus),
      m_orient(aFootprint.m_orient),
      m_lastEditTime(aFootprint.m_lastEditTime),
      m_link(aFootprint.m_link),
      m_path(aFootprint.m_path),
      m_embedFonts(aFootprint.m_embedFonts),
      m_cachedBoundingBox(aFootprint.m_cachedBoundingBox),
      m_boundingBoxCacheTimeStamp(aFootprint.m_boundingBoxCacheTimeStamp),
      m_cachedTextExcludedBBox(aFootprint.m_cachedTextExcludedBBox),
      m_textExcludedBBoxCacheTimeStamp(aFootprint.m_textExcludedBBoxCacheTimeStamp),
      m_cachedHull(aFootprint.m_cachedHull),
      m_hullCacheTimeStamp(aFootprint.m_hullCacheTimeStamp),
      m_clearance(aFootprint.m_clearance),
      m_solderMaskMargin(aFootprint.m_solderMaskMargin),
      m_solderPasteMargin(aFootprint.m_solderPasteMargin),
      m_solderPasteMarginRatio(aFootprint.m_solderPasteMarginRatio),
      m_zoneConnection(aFootprint.m_zoneConnection),
      m_netTiePadGroups(aFootprint.m_netTiePadGroups),
      m_fileFormatVersionAtLoad(aFootprint.m_fileFormatVersionAtLoad),
      m_componentClass(aFootprint.m_componentClass)
{
    QMap<EDA_BOARD_OBJECT_DATA*, EDA_BOARD_OBJECT_DATA*> ptrMap;
    
    // Initialize mandatory fields
    for (int i = 0; i < 5; ++i)
        m_fields.append(nullptr);
    
    // Copy fields
    for (EDA_BRD_FIELD_DATA* field : aFootprint.m_fields) {
        if (field) {
            EDA_BRD_FIELD_DATA* newField = static_cast<EDA_BRD_FIELD_DATA*>(field->clone());
            ptrMap[field] = newField;
            add(newField, QtAddMode::Append);
        } else {
            m_fields.append(nullptr);
        }
    }

    // Copy pads
    for (EDA_PAD_DATA* pad : aFootprint.pads()) {
        EDA_PAD_DATA* newPad = static_cast<EDA_PAD_DATA*>(pad->clone());
        ptrMap[pad] = newPad;
        add(newPad, QtAddMode::Append);
    }

    // Copy zones
    for (EDA_ZONE_DATA* zone : aFootprint.zones()) {
        EDA_ZONE_DATA* newZone = static_cast<EDA_ZONE_DATA*>(zone->clone());
        ptrMap[zone] = newZone;
        add(newZone, QtAddMode::Append);
        newZone->setNetCode(-1);
    }

    // Copy drawings
    for (EDA_BOARD_OBJECT_DATA* item : aFootprint.graphicalItems()) {
        EDA_BOARD_OBJECT_DATA* newItem = static_cast<EDA_BOARD_OBJECT_DATA*>(item->clone());
        ptrMap[item] = newItem;
        add(newItem, QtAddMode::Append);
    }

    // Copy groups
    for (QtPcbGroup* group : aFootprint.groups()) {
        QtPcbGroup* newGroup = static_cast<QtPcbGroup*>(group->clone());
        ptrMap[group] = newGroup;
        add(newGroup, QtAddMode::Append);
    }
    
    // Rebuild groups
    for (QtPcbGroup* group : aFootprint.groups()) {
        QtPcbGroup* newGroup = static_cast<QtPcbGroup*>(ptrMap[group]);
        newGroup->getItems().clear();
        
        for (EDA_BOARD_OBJECT_DATA* member : group->getItems()) {
            if (ptrMap.contains(member))
                newGroup->addItem(ptrMap[member]);
        }
    }
    
    // Copy auxiliary data
    m_3DDrawings = aFootprint.m_3DDrawings;
    m_libDescription = aFootprint.m_libDescription;
    m_keywords = aFootprint.m_keywords;
    m_privateLayers = aFootprint.m_privateLayers;
    
    m_arflag = 0;
    m_initialComments = aFootprint.m_initialComments ? 
                        new QStringList(*aFootprint.m_initialComments) : nullptr;
}

// Move constructor
EDA_FOOTPRINT_DATA::EDA_FOOTPRINT_DATA(EDA_FOOTPRINT_DATA&& aFootprint)
    : EDA_BOARD_OBJECT_CONTAINER(aFootprint)
{
    *this = std::move(aFootprint);
}

// Destructor
EDA_FOOTPRINT_DATA::~EDA_FOOTPRINT_DATA()
{
    // Untangle group parents before deleting
    for (QtPcbGroup* group : m_groups) {
        for (EDA_BOARD_OBJECT_DATA* item : group->getItems())
            item->setParentGroup(nullptr);
    }
    
    // Clean up owned elements
    delete m_initialComments;
    
    for (EDA_BRD_FIELD_DATA* f : m_fields)
        delete f;
    m_fields.clear();
    
    for (EDA_PAD_DATA* p : m_pads)
        delete p;
    m_pads.clear();
    
    for (EDA_ZONE_DATA* zone : m_zones)
        delete zone;
    m_zones.clear();
    
    for (QtPcbGroup* group : m_groups)
        delete group;
    m_groups.clear();
    
    for (EDA_BOARD_OBJECT_DATA* d : m_drawings)
        delete d;
    m_drawings.clear();
    
    if (EDA_BOARD_DATA* board = getBoard())
        board->incrementTimeStamp();
}

// Assignment operators
EDA_FOOTPRINT_DATA& EDA_FOOTPRINT_DATA::operator=(const EDA_FOOTPRINT_DATA& aOther)
{
    if (this == &aOther)
        return *this;
    
    EDA_BOARD_OBJECT_DATA::operator=(aOther);
    
    m_pos = aOther.m_pos;
    m_fpid = aOther.m_fpid;
    m_attributes = aOther.m_attributes;
    m_fpStatus = aOther.m_fpStatus;
    m_orient = aOther.m_orient;
    m_lastEditTime = aOther.m_lastEditTime;
    m_link = aOther.m_link;
    m_path = aOther.m_path;
    
    m_cachedBoundingBox = aOther.m_cachedBoundingBox;
    m_boundingBoxCacheTimeStamp = aOther.m_boundingBoxCacheTimeStamp;
    m_cachedTextExcludedBBox = aOther.m_cachedTextExcludedBBox;
    m_textExcludedBBoxCacheTimeStamp = aOther.m_textExcludedBBoxCacheTimeStamp;
    m_cachedHull = aOther.m_cachedHull;
    m_hullCacheTimeStamp = aOther.m_hullCacheTimeStamp;
    
    m_clearance = aOther.m_clearance;
    m_solderMaskMargin = aOther.m_solderMaskMargin;
    m_solderPasteMargin = aOther.m_solderPasteMargin;
    m_solderPasteMarginRatio = aOther.m_solderPasteMarginRatio;
    m_zoneConnection = aOther.m_zoneConnection;
    m_netTiePadGroups = aOther.m_netTiePadGroups;
    m_componentClass = aOther.m_componentClass;
    
    QMap<EDA_BOARD_OBJECT_DATA*, EDA_BOARD_OBJECT_DATA*> ptrMap;
    
    // Copy fields
    m_fields.clear();
    for (EDA_BRD_FIELD_DATA* field : aOther.m_fields) {
        if (field) {
            EDA_BRD_FIELD_DATA* newField = new EDA_BRD_FIELD_DATA(*field);
            ptrMap[field] = newField;
            add(newField);
        } else {
            m_fields.append(nullptr);
        }
    }
    
    // Copy pads
    m_pads.clear();
    for (EDA_PAD_DATA* pad : aOther.pads()) {
        EDA_PAD_DATA* newPad = new EDA_PAD_DATA(*pad);
        ptrMap[pad] = newPad;
        add(newPad);
    }
    
    // Copy zones
    m_zones.clear();
    for (EDA_ZONE_DATA* zone : aOther.zones()) {
        EDA_ZONE_DATA* newZone = static_cast<EDA_ZONE_DATA*>(zone->clone());
        ptrMap[zone] = newZone;
        add(newZone);
        newZone->setNetCode(-1);
    }
    
    // Copy drawings
    m_drawings.clear();
    for (EDA_BOARD_OBJECT_DATA* item : aOther.graphicalItems()) {
        EDA_BOARD_OBJECT_DATA* newItem = static_cast<EDA_BOARD_OBJECT_DATA*>(item->clone());
        ptrMap[item] = newItem;
        add(newItem);
    }
    
    // Copy groups
    m_groups.clear();
    for (QtPcbGroup* group : aOther.groups()) {
        QtPcbGroup* newGroup = static_cast<QtPcbGroup*>(group->clone());
        newGroup->getItems().clear();
        
        for (EDA_BOARD_OBJECT_DATA* member : group->getItems())
            newGroup->addItem(ptrMap[member]);
        
        add(newGroup);
    }
    
    // Copy auxiliary data
    m_3DDrawings = aOther.m_3DDrawings;
    m_libDescription = aOther.m_libDescription;
    m_keywords = aOther.m_keywords;
    m_privateLayers = aOther.m_privateLayers;
    
    m_initialComments = aOther.m_initialComments ?
                        new QStringList(*aOther.m_initialComments) : nullptr;
    
    return *this;
}

EDA_FOOTPRINT_DATA& EDA_FOOTPRINT_DATA::operator=(EDA_FOOTPRINT_DATA&& aOther)
{
    if (this == &aOther)
        return *this;
    
    EDA_BOARD_OBJECT_DATA::operator=(std::move(aOther));
    
    m_pos = aOther.m_pos;
    m_fpid = aOther.m_fpid;
    m_attributes = aOther.m_attributes;
    m_fpStatus = aOther.m_fpStatus;
    m_orient = aOther.m_orient;
    m_lastEditTime = aOther.m_lastEditTime;
    m_link = aOther.m_link;
    m_path = aOther.m_path;
    m_embedFonts = aOther.m_embedFonts;
    
    m_cachedBoundingBox = aOther.m_cachedBoundingBox;
    m_boundingBoxCacheTimeStamp = aOther.m_boundingBoxCacheTimeStamp;
    m_cachedTextExcludedBBox = aOther.m_cachedTextExcludedBBox;
    m_textExcludedBBoxCacheTimeStamp = aOther.m_textExcludedBBoxCacheTimeStamp;
    m_cachedHull = aOther.m_cachedHull;
    m_hullCacheTimeStamp = aOther.m_hullCacheTimeStamp;
    
    m_clearance = aOther.m_clearance;
    m_solderMaskMargin = aOther.m_solderMaskMargin;
    m_solderPasteMargin = aOther.m_solderPasteMargin;
    m_solderPasteMarginRatio = aOther.m_solderPasteMarginRatio;
    m_zoneConnection = aOther.m_zoneConnection;
    m_netTiePadGroups = aOther.m_netTiePadGroups;
    
    m_componentClass = aOther.m_componentClass;
    
    // Move fields
    m_fields.clear();
    addMandatoryFields();
    
    for (EDA_BRD_FIELD_DATA* field : aOther.m_fields) {
        if (!field)
            continue;
        
        if (!field->isMandatory())
            add(field);
        else
            *getField(field->getId()) = *field;
    }
    
    // Move pads
    m_pads.clear();
    for (EDA_PAD_DATA* pad : aOther.pads())
        add(pad);
    aOther.pads().clear();
    
    // Move zones
    m_zones.clear();
    for (EDA_ZONE_DATA* item : aOther.zones()) {
        add(item);
        item->setNetCode(-1);
    }
    aOther.zones().clear();
    
    // Move drawings
    m_drawings.clear();
    for (EDA_BOARD_OBJECT_DATA* item : aOther.graphicalItems())
        add(item);
    aOther.graphicalItems().clear();
    
    // Move groups
    m_groups.clear();
    for (QtPcbGroup* group : aOther.groups())
        add(group);
    aOther.groups().clear();
    
    // Move auxiliary data
    m_3DDrawings = std::move(aOther.m_3DDrawings);
    m_libDescription = std::move(aOther.m_libDescription);
    m_keywords = std::move(aOther.m_keywords);
    m_privateLayers = std::move(aOther.m_privateLayers);
    
    m_initialComments = aOther.m_initialComments;
    aOther.m_initialComments = nullptr;
    
    // Clear the other footprint's containers
    aOther.m_fields.clear();
    aOther.pads().clear();
    aOther.zones().clear();
    aOther.graphicalItems().clear();
    
    return *this;
}

bool EDA_FOOTPRINT_DATA::classOf(const EDA_BOARD_OBJECT_DATA* aItem)
{
    return aItem && aItem->getType() == PCB_FOOTPRINT_T;
}

void EDA_FOOTPRINT_DATA::addMandatoryFields()
{
    auto addField = [this](int id, QtPcbLayerId layer, bool visible) {
        EDA_BRD_FIELD_DATA* field = new EDA_BRD_FIELD_DATA(this, id);
        field->setLayer(layer);
        field->setVisible(visible);
        m_fields.append(field);
    };
    
    // These are the mandatory fields
    addField(0, QtPcbLayerId::FSilkS, true);  // REFERENCE
    addField(1, QtPcbLayerId::FFab, true);    // VALUE
    m_fields.append(nullptr);                   // FOOTPRINT_FIELD
    addField(3, QtPcbLayerId::FFab, false);   // DATASHEET
    addField(4, QtPcbLayerId::FFab, false);   // DESCRIPTION
}

// Position and orientation
void EDA_FOOTPRINT_DATA::setPosition(const QPointF& aPos)
{
    QPointF delta = aPos - m_pos;
    m_pos = aPos;
    
    // Move all child items
    for (EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field)
            field->move(delta);
    }
    
    for (EDA_PAD_DATA* pad : m_pads)
        pad->move(delta);
    
    for (EDA_BOARD_OBJECT_DATA* item : m_drawings)
        item->move(delta);
    
    for (EDA_ZONE_DATA* zone : m_zones)
        zone->move(delta);
    
    for (QtPcbGroup* group : m_groups)
        group->move(delta);
    
    m_boundingBoxCacheTimeStamp = 0;
    m_textExcludedBBoxCacheTimeStamp = 0;
    m_hullCacheTimeStamp = 0;
}

void EDA_FOOTPRINT_DATA::setOrientation(const QtEdaAngle& aNewAngle)
{
    QtEdaAngle angleChange(aNewAngle.value - m_orient.value);
    
    if (angleChange.value != 0) {
        m_orient = aNewAngle;
        m_orient.normalize();
        
        // Rotate all child items around footprint origin
        for (EDA_BRD_FIELD_DATA* field : m_fields) {
            if (field)
                field->rotate(m_pos, angleChange);
        }
        
        for (EDA_PAD_DATA* pad : m_pads)
            pad->rotate(m_pos, angleChange);
        
        for (EDA_BOARD_OBJECT_DATA* item : m_drawings)
            item->rotate(m_pos, angleChange);
        
        for (EDA_ZONE_DATA* zone : m_zones)
            zone->rotate(m_pos, angleChange);
        
        for (QtPcbGroup* group : m_groups)
            group->rotate(m_pos, angleChange);
        
        m_boundingBoxCacheTimeStamp = 0;
        m_textExcludedBBoxCacheTimeStamp = 0;
        m_hullCacheTimeStamp = 0;
    }
}

// Layer management
void EDA_FOOTPRINT_DATA::setLayerAndFlip(QtPcbLayerId aLayer)
{
    if (aLayer != getLayer()) {
        flip(m_pos, QtFlipDirection::LeftRight);
        setLayer(aLayer);
    }
}

QtPcbLayerId EDA_FOOTPRINT_DATA::getLayer() const
{
    return EDA_BOARD_OBJECT_DATA::getLayer();
}

QtPcbLayerId EDA_FOOTPRINT_DATA::getSide() const
{
    if (getLayer() == QtPcbLayerId::FCu)
        return QtPcbLayerId::FCu;
    else if (getLayer() == QtPcbLayerId::BCu)
        return QtPcbLayerId::BCu;
    else
        return QtPcbLayerId::UndefinedLayer;
}

bool EDA_FOOTPRINT_DATA::isFlipped() const
{
    return getLayer() == QtPcbLayerId::BCu;
}

bool EDA_FOOTPRINT_DATA::isOnLayer(QtPcbLayerId aLayer) const
{
    // Check footprint layer
    if (getLayer() == aLayer)
        return true;
    
    // Check if any pad is on the layer
    for (const EDA_PAD_DATA* pad : m_pads) {
        if (pad->isOnLayer(aLayer))
            return true;
    }
    
    // Check graphical items
    for (const EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        if (item->isOnLayer(aLayer))
            return true;
    }
    
    // Check zones
    for (const EDA_ZONE_DATA* zone : m_zones) {
        if (zone->isOnLayer(aLayer))
            return true;
    }
    
    return false;
}

// Field management
const QString& EDA_FOOTPRINT_DATA::getReference() const
{
    return reference().getText();
}

void EDA_FOOTPRINT_DATA::setReference(const QString& aReference)
{
    reference().setText(aReference);
    // Fields changed notification removed (Qt object system removal)
}

const QString& EDA_FOOTPRINT_DATA::getValue() const
{
    return value().getText();
}

void EDA_FOOTPRINT_DATA::setValue(const QString& aValue)
{
    value().setText(aValue);
    // Fields changed notification removed (Qt object system removal)
}

EDA_BRD_FIELD_DATA& EDA_FOOTPRINT_DATA::value()
{
    return *getField(1); // VALUE_FIELD
}

EDA_BRD_FIELD_DATA& EDA_FOOTPRINT_DATA::reference()
{
    return *getField(0); // REFERENCE_FIELD
}

const EDA_BRD_FIELD_DATA& EDA_FOOTPRINT_DATA::value() const
{
    return *getField(1);
}

const EDA_BRD_FIELD_DATA& EDA_FOOTPRINT_DATA::reference() const
{
    return *getField(0);
}

EDA_BRD_FIELD_DATA* EDA_FOOTPRINT_DATA::getField(int aFieldType)
{
    if (aFieldType >= 0 && aFieldType < m_fields.size())
        return m_fields[aFieldType];
    return nullptr;
}

const EDA_BRD_FIELD_DATA* EDA_FOOTPRINT_DATA::getField(int aFieldType) const
{
    if (aFieldType >= 0 && aFieldType < m_fields.size())
        return m_fields[aFieldType];
    return nullptr;
}

EDA_BRD_FIELD_DATA* EDA_FOOTPRINT_DATA::getFieldById(int aFieldId)
{
    for (EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field && field->getId() == aFieldId)
            return field;
    }
    return nullptr;
}

EDA_BRD_FIELD_DATA* EDA_FOOTPRINT_DATA::getFieldByName(const QString& aFieldName)
{
    for (EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field && field->getName() == aFieldName)
            return field;
    }
    return nullptr;
}

bool EDA_FOOTPRINT_DATA::hasFieldByName(const QString& aFieldName) const
{
    for (const EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field && field->getName() == aFieldName)
            return true;
    }
    return false;
}

QString EDA_FOOTPRINT_DATA::getFieldText(const QString& aFieldName) const
{
    for (const EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field && field->getName() == aFieldName)
            return field->getText();
    }
    return QString();
}

void EDA_FOOTPRINT_DATA::getFields(QVector<EDA_BRD_FIELD_DATA*>& aVector, bool aVisibleOnly) const
{
    for (EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field && (!aVisibleOnly || field->isVisible()))
            aVector.append(field);
    }
}

QVector<EDA_BRD_FIELD_DATA*> EDA_FOOTPRINT_DATA::getFields(bool aVisibleOnly) const
{
    QVector<EDA_BRD_FIELD_DATA*> fields;
    getFields(fields, aVisibleOnly);
    return fields;
}

void EDA_FOOTPRINT_DATA::clearFields()
{
    m_fields.clear();
    // Fields changed notification removed (Qt object system removal)
}

EDA_BRD_FIELD_DATA* EDA_FOOTPRINT_DATA::addField(const EDA_BRD_FIELD_DATA& aField)
{
    EDA_BRD_FIELD_DATA* newField = new EDA_BRD_FIELD_DATA(aField);
    newField->setEdaParent(this);
    m_fields.append(newField);
    // Fields changed notification removed (Qt object system removal)
    return newField;
}

void EDA_FOOTPRINT_DATA::removeField(const QString& aFieldName)
{
    for (int i = 0; i < m_fields.size(); ++i) {
        EDA_BRD_FIELD_DATA* field = m_fields[i];
        if (field && !field->isMandatory() && field->getName() == aFieldName) {
            m_fields.removeAt(i);
            delete field;
            // Fields changed notification removed (Qt object system removal)
            break;
        }
    }
}

int EDA_FOOTPRINT_DATA::getNextFieldId() const
{
    return m_fields.size();
}

// Container management
void EDA_FOOTPRINT_DATA::add(EDA_BOARD_OBJECT_DATA* aBoardItem, QtAddMode aMode, bool aSkipConnectivity)
{
    switch (aBoardItem->getType()) {
    case PCB_FIELD_T:
    {
        EDA_BRD_FIELD_DATA* field = static_cast<EDA_BRD_FIELD_DATA*>(aBoardItem);
        
        if (field->isMandatory()) {
            if (m_fields.size() >= 5 && m_fields[field->getId()] == nullptr) {
                m_fields[field->getId()] = field;
            }
        } else {
            m_fields.append(field);
        }
        // Fields changed notification removed (Qt object system removal)
        break;
    }
    
    case PCB_TEXT_T:
    case PCB_SHAPE_T:
    case PCB_TEXTBOX_T:
    case PCB_TABLE_T:
    case PCB_REFERENCE_IMAGE_T:
    case PCB_DIM_ALIGNED_T:  // All dimension types map to PcbDimensionT
        if (aMode == QtAddMode::Append)
            m_drawings.append(aBoardItem);
        else
            m_drawings.prepend(aBoardItem);
        break;

    case PCB_PAD_T:
        if (aMode == QtAddMode::Append)
            m_pads.append(static_cast<EDA_PAD_DATA*>(aBoardItem));
        else
            m_pads.prepend(static_cast<EDA_PAD_DATA*>(aBoardItem));
        // Pads changed notification removed (Qt object system removal)
        break;

    case PCB_ZONE_T:
        if (aMode == QtAddMode::Append)
            m_zones.append(static_cast<EDA_ZONE_DATA*>(aBoardItem));
        else
            m_zones.insert(0, static_cast<EDA_ZONE_DATA*>(aBoardItem));
        break;

    case PCB_GROUP_T:
        if (aMode == QtAddMode::Append)
            m_groups.append(static_cast<QtPcbGroup*>(aBoardItem));
        else
            m_groups.insert(0, static_cast<QtPcbGroup*>(aBoardItem));
        break;
    
    default:
        qWarning() << "EDA_FOOTPRINT_DATA::add() - unhandled item type:" << aBoardItem->getType();
        return;
    }
    
    aBoardItem->clearEditFlags();
    aBoardItem->setEdaParent(this);
}

void EDA_FOOTPRINT_DATA::remove(EDA_BOARD_OBJECT_DATA* aBoardItem, QtRemoveMode aMode)
{
    switch (aBoardItem->getType()) {
    case PCB_FIELD_T:
    {
        EDA_BRD_FIELD_DATA* field = static_cast<EDA_BRD_FIELD_DATA*>(aBoardItem);
        
        if (field->isMandatory()) {
            m_fields[field->getId()] = nullptr;
        } else {
            m_fields.removeOne(field);
        }
        // Fields changed notification removed (Qt object system removal)
        break;
    }
    
    case PCB_TEXT_T:
    case PCB_SHAPE_T:
    case PCB_TEXTBOX_T:
    case PCB_TABLE_T:
    case PCB_REFERENCE_IMAGE_T:
    case PCB_DIM_ALIGNED_T:  // All dimension types map to PcbDimensionT
        m_drawings.removeOne(aBoardItem);
        break;
    
    case PCB_PAD_T:
        m_pads.removeOne(static_cast<EDA_PAD_DATA*>(aBoardItem));
        // Pads changed notification removed (Qt object system removal)
        break;
    
    case PCB_ZONE_T:
        m_zones.removeOne(static_cast<EDA_ZONE_DATA*>(aBoardItem));
        break;
    
    case PCB_GROUP_T:
        m_groups.removeOne(static_cast<QtPcbGroup*>(aBoardItem));
        break;
    
    default:
        qWarning() << "EDA_FOOTPRINT_DATA::remove() - unhandled item type:" << aBoardItem->getType();
        return;
    }
    
    if (aMode == QtRemoveMode::Normal)
        delete aBoardItem;
}

// Net management
void EDA_FOOTPRINT_DATA::clearAllNets()
{
    // Force orphaned net for all pads
    for (EDA_PAD_DATA* pad : m_pads)
        pad->setNetCode(-1); // ORPHANED
}

bool EDA_FOOTPRINT_DATA::fixUuids()
{
    bool changed = false;

    if (getUuid().isNull()) {
        // Note: UUID is const, so we can't modify it directly
        // This method may need to be reconsidered in the design
        changed = true;
    }

    for (EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field && field->getUuid().isNull()) {
            // Note: UUID is const, so we can't modify it directly
            // This method may need to be reconsidered in the design
            changed = true;
        }
    }

    for (EDA_PAD_DATA* pad : m_pads) {
        if (pad->getUuid().isNull()) {
            // Note: UUID is const, so we can't modify it directly
            // This method may need to be reconsidered in the design
            changed = true;
        }
    }

    for (EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        if (item->getUuid().isNull()) {
            // Note: UUID is const, so we can't modify it directly
            // This method may need to be reconsidered in the design
            changed = true;
        }
    }

    for (EDA_ZONE_DATA* zone : m_zones) {
        if (zone->getUuid().isNull()) {
            // Note: UUID is const, so we can't modify it directly
            // This method may need to be reconsidered in the design
            changed = true;
        }
    }

    return changed;
}

// Attributes
void EDA_FOOTPRINT_DATA::setBoardOnly(bool aIsBoardOnly)
{
    if (aIsBoardOnly)
        m_attributes |= FP_BOARD_ONLY;
    else
        m_attributes &= ~FP_BOARD_ONLY;
    // Attributes changed notification removed (Qt object system removal)
}

void EDA_FOOTPRINT_DATA::setExcludedFromPosFiles(bool aExclude)
{
    if (aExclude)
        m_attributes |= FP_EXCLUDE_FROM_POS_FILES;
    else
        m_attributes &= ~FP_EXCLUDE_FROM_POS_FILES;
    // Attributes changed notification removed (Qt object system removal)
}

void EDA_FOOTPRINT_DATA::setExcludedFromBOM(bool aExclude)
{
    if (aExclude)
        m_attributes |= FP_EXCLUDE_FROM_BOM;
    else
        m_attributes &= ~FP_EXCLUDE_FROM_BOM;
    // Attributes changed notification removed (Qt object system removal)
}

void EDA_FOOTPRINT_DATA::setAllowMissingCourtyard(bool aAllow)
{
    if (aAllow)
        m_attributes |= FP_ALLOW_MISSING_COURTYARD;
    else
        m_attributes &= ~FP_ALLOW_MISSING_COURTYARD;
    // Attributes changed notification removed (Qt object system removal)
}

void EDA_FOOTPRINT_DATA::setDNP(bool aDNP)
{
    if (aDNP)
        m_attributes |= FP_DNP;
    else
        m_attributes &= ~FP_DNP;
    // Attributes changed notification removed (Qt object system removal)
}

// Status flags
bool EDA_FOOTPRINT_DATA::isLocked() const
{
    return (m_fpStatus & FP_is_LOCKED) != 0;
}

void EDA_FOOTPRINT_DATA::setLocked(bool isLocked)
{
    if (isLocked)
        m_fpStatus |= FP_is_LOCKED;
    else
        m_fpStatus &= ~FP_is_LOCKED;
}

bool EDA_FOOTPRINT_DATA::isConflicting() const
{
    // COURTYARD_CONFLICT is not defined in the current flag system
    // For now, return false as a placeholder
    return false;
}

bool EDA_FOOTPRINT_DATA::isPlaced() const
{
    return m_fpStatus & FP_is_PLACED;
}

void EDA_FOOTPRINT_DATA::setIsPlaced(bool isPlaced)
{
    if (isPlaced)
        m_fpStatus |= FP_is_PLACED;
    else
        m_fpStatus &= ~FP_is_PLACED;
}

bool EDA_FOOTPRINT_DATA::needsPlaced() const
{
    return m_fpStatus & FP_to_PLACE;
}

void EDA_FOOTPRINT_DATA::setNeedsPlaced(bool needsPlaced)
{
    if (needsPlaced)
        m_fpStatus |= FP_to_PLACE;
    else
        m_fpStatus &= ~FP_to_PLACE;
}

bool EDA_FOOTPRINT_DATA::legacyPadsLocked() const
{
    return m_fpStatus & FP_PADS_are_LOCKED;
}

// Net tie management
bool EDA_FOOTPRINT_DATA::isNetTie() const
{
    for (const QString& group : m_netTiePadGroups) {
        if (!group.isEmpty())
            return true;
    }
    return false;
}

QMap<QString, int> EDA_FOOTPRINT_DATA::mapPadNumbersToNetTieGroups() const
{
    QMap<QString, int> padNumberToGroupIndex;
    
    for (int i = 0; i < m_netTiePadGroups.size(); ++i) {
        QStringList padNumbers = m_netTiePadGroups[i].split(',', Qt::SkipEmptyParts);
        for (const QString& padNumber : padNumbers) {
            padNumberToGroupIndex[padNumber.trimmed()] = i;
        }
    }
    
    return padNumberToGroupIndex;
}

QVector<EDA_PAD_DATA*> EDA_FOOTPRINT_DATA::getNetTiePads(EDA_PAD_DATA* aPad) const
{
    QVector<EDA_PAD_DATA*> connectedPads;
    
    QMap<QString, int> padToGroup = mapPadNumbersToNetTieGroups();
    auto it = padToGroup.find(aPad->getNumber());
    
    if (it != padToGroup.end()) {
        int groupIndex = it.value();
        QStringList padNumbers = m_netTiePadGroups[groupIndex].split(',', Qt::SkipEmptyParts);
        
        for (const QString& padNumber : padNumbers) {
            QString trimmedNumber = padNumber.trimmed();
            if (trimmedNumber != aPad->getNumber()) {
                EDA_PAD_DATA* pad = findPadByNumber(trimmedNumber);
                if (pad)
                    connectedPads.append(pad);
            }
        }
    }
    
    return connectedPads;
}

void EDA_FOOTPRINT_DATA::buildNetTieCache()
{
    m_netTieCache.clear();
    
    QMap<QString, int> padToGroupMap = mapPadNumbersToNetTieGroups();
    
    for (const EDA_PAD_DATA* pad : m_pads) {
        auto it = padToGroupMap.find(pad->getNumber());
        if (it != padToGroupMap.end()) {
            int groupIdx = it.value();
            QStringList padsInGroup = m_netTiePadGroups[groupIdx].split(',', Qt::SkipEmptyParts);
            
            QSet<int> nets;
            for (const QString& padNumber : padsInGroup) {
                EDA_PAD_DATA* groupPad = findPadByNumber(padNumber.trimmed());
                if (groupPad)
                    nets.insert(groupPad->getNetCode());
            }
            
            m_netTieCache[pad] = nets;
        }
    }
}

const QSet<int>& EDA_FOOTPRINT_DATA::getNetTieCache(const EDA_BOARD_OBJECT_DATA* aItem) const
{
    static const QSet<int> emptySet;
    
    auto it = m_netTieCache.find(aItem);
    if (it == m_netTieCache.end())
        return emptySet;
    
    return it.value();
}

// Pad operations
EDA_PAD_DATA* EDA_FOOTPRINT_DATA::findPadByNumber(const QString& aPadNumber, EDA_PAD_DATA* aSearchAfterMe) const
{
    bool startSearching = (aSearchAfterMe == nullptr);
    
    for (EDA_PAD_DATA* pad : m_pads) {
        if (!startSearching && pad == aSearchAfterMe) {
            startSearching = true;
            continue;
        }
        
        if (startSearching && pad->getNumber() == aPadNumber)
            return pad;
    }
    
    return nullptr;
}

EDA_PAD_DATA* EDA_FOOTPRINT_DATA::getPad(const QPointF& aPosition, QtLayerSet aLayerMask)
{
    for (EDA_PAD_DATA* pad : m_pads) {
        if (pad->hitTest(aPosition, 0) && (pad->getLayerSet() & aLayerMask).any())
            return pad;
    }
    return nullptr;
}

QVector<const EDA_PAD_DATA*> EDA_FOOTPRINT_DATA::getPads(const QString& aPadNumber, const EDA_PAD_DATA* aIgnore) const
{
    QVector<const EDA_PAD_DATA*> pads;
    
    for (const EDA_PAD_DATA* pad : m_pads) {
        if (pad != aIgnore && pad->getNumber() == aPadNumber)
            pads.append(pad);
    }
    
    return pads;
}

unsigned EDA_FOOTPRINT_DATA::getPadCount(QtIncludeNpthT aIncludeNPTH) const
{
    if (aIncludeNPTH)
        return m_pads.size();
    
    unsigned count = 0;
    for (const EDA_PAD_DATA* pad : m_pads) {
        if (pad->getAttribute() != QtPadAttribute::NPTH)
            count++;
    }
    
    return count;
}

unsigned EDA_FOOTPRINT_DATA::getUniquePadCount(QtIncludeNpthT aIncludeNPTH) const
{
    return getUniquePadNumbers(aIncludeNPTH).size();
}

QSet<QString> EDA_FOOTPRINT_DATA::getUniquePadNumbers(QtIncludeNpthT aIncludeNPTH) const
{
    QSet<QString> unique;
    
    for (const EDA_PAD_DATA* pad : m_pads) {
        if (!pad->getNumber().isEmpty() && 
            (aIncludeNPTH || pad->getAttribute() != QtPadAttribute::NPTH)) {
            unique.insert(pad->getNumber());
        }
    }
    
    return unique;
}

QString EDA_FOOTPRINT_DATA::getNextPadNumber(const QString& aLastPadName) const
{
    // Extract numeric part from last pad name
    int lastNum = 0;
    QString prefix;
    
    for (int i = aLastPadName.length() - 1; i >= 0; --i) {
        if (aLastPadName[i].isDigit()) {
            bool ok;
            lastNum = aLastPadName.mid(i).toInt(&ok);
            if (ok) {
                prefix = aLastPadName.left(i);
                break;
            }
        }
    }
    
    if (lastNum == 0) {
        // No number found, try to increment the whole string
        if (aLastPadName.isEmpty())
            return "1";
        
        QChar lastChar = aLastPadName[aLastPadName.length() - 1];
        if (lastChar.isLetter()) {
            if (lastChar == 'Z' || lastChar == 'z')
                return aLastPadName + "1";
            else
                return aLastPadName.left(aLastPadName.length() - 1) + 
                       QChar(lastChar.unicode() + 1);
        }
        
        return aLastPadName + "1";
    }
    
    return prefix + QString::number(lastNum + 1);
}

bool EDA_FOOTPRINT_DATA::hasThroughHolePads() const
{
    for (const EDA_PAD_DATA* pad : m_pads) {
        if (pad->getAttribute() == QtPadAttribute::PTH ||
            pad->getAttribute() == QtPadAttribute::NPTH)
            return true;
    }
    return false;
}

// Geometry operations
QRectF EDA_FOOTPRINT_DATA::getBoundingBox() const
{
    return getBoundingBox(true);
}

const QRectF EDA_FOOTPRINT_DATA::getBoundingBox(bool aIncludeText) const
{
    if (m_boundingBoxCacheTimeStamp >= getBoard()->getTimeStamp() && !aIncludeText)
        return m_cachedBoundingBox;
    
    if (m_textExcludedBBoxCacheTimeStamp >= getBoard()->getTimeStamp() && aIncludeText)
        return m_cachedTextExcludedBBox;
    
    QRectF bbox;
    
    // Include pads
    for (const EDA_PAD_DATA* pad : m_pads) {
        if (bbox.isNull())
            bbox = pad->getBoundingBox();
        else
            bbox = bbox.united(pad->getBoundingBox());
    }
    
    // Include graphical items
    for (const EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        if (!aIncludeText && item->getType() == PCB_TEXT_T)
            continue;
        
        if (bbox.isNull())
            bbox = item->getBoundingBox();
        else
            bbox = bbox.united(item->getBoundingBox());
    }
    
    // Include zones
    for (const EDA_ZONE_DATA* zone : m_zones) {
        if (bbox.isNull())
            bbox = zone->getBoundingBox();
        else
            bbox = bbox.united(zone->getBoundingBox());
    }
    
    // Include fields if including text
    if (aIncludeText) {
        for (const EDA_BRD_FIELD_DATA* field : m_fields) {
            if (field && field->isVisible()) {
                if (bbox.isNull())
                    bbox = field->getBoundingBox();
                else
                    bbox = bbox.united(field->getBoundingBox());
            }
        }
    }
    
    // Cache the result
    if (aIncludeText) {
        m_cachedTextExcludedBBox = bbox;
        m_textExcludedBBoxCacheTimeStamp = getBoard()->getTimeStamp();
    } else {
        m_cachedBoundingBox = bbox;
        m_boundingBoxCacheTimeStamp = getBoard()->getTimeStamp();
    }
    
    return bbox;
}

const QRectF EDA_FOOTPRINT_DATA::getLayerBoundingBox(QtLayerSet aLayers) const
{
    QRectF bbox;
    
    // Include pads on specified layers
    for (const EDA_PAD_DATA* pad : m_pads) {
        if ((pad->getLayerSet() & aLayers).any()) {
            if (bbox.isNull())
                bbox = pad->getBoundingBox();
            else
                bbox = bbox.united(pad->getBoundingBox());
        }
    }
    
    // Include graphical items on specified layers
    for (const EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        if (aLayers.test(item->getLayer())) {
            if (bbox.isNull())
                bbox = item->getBoundingBox();
            else
                bbox = bbox.united(item->getBoundingBox());
        }
    }
    
    // Include zones on specified layers
    for (const EDA_ZONE_DATA* zone : m_zones) {
        if ((zone->getLayerSet() & aLayers).any()) {
            if (bbox.isNull())
                bbox = zone->getBoundingBox();
            else
                bbox = bbox.united(zone->getBoundingBox());
        }
    }
    
    return bbox;
}

QPointF EDA_FOOTPRINT_DATA::getCenter() const
{
    return getBoundingBox(false).center();
}

QRectF EDA_FOOTPRINT_DATA::getFpPadsLocalBbox() const
{
    QRectF bbox;
    
    // Calculate bbox for pads in local coordinates (origin 0,0, no rotation)
    for (const EDA_PAD_DATA* pad : m_pads) {
        QPointF padPos = pad->getPosition() - m_pos;
        
        // Rotate back to 0 orientation
        if (m_orient.value != 0) {
            double angle = -m_orient.value * M_PI / 180.0;
            double cos_a = cos(angle);
            double sin_a = sin(angle);
            
            double x = padPos.x() * cos_a - padPos.y() * sin_a;
            double y = padPos.x() * sin_a + padPos.y() * cos_a;
            
            padPos = QPointF(x, y);
        }
        
        // Create a simple bounding box for the pad
        // This is simplified - real implementation would consider pad shape
        QSizeF padSize(1000000, 1000000); // 1mm default
        QRectF padBbox(padPos - QPointF(padSize.width()/2, padSize.height()/2), padSize);
        
        if (bbox.isNull())
            bbox = padBbox;
        else
            bbox = bbox.united(padBbox);
    }
    
    return bbox;
}

QtShapePolySet EDA_FOOTPRINT_DATA::getBoundingHull() const
{
    if (m_hullCacheTimeStamp >= getBoard()->getTimeStamp())
        return m_cachedHull;
    
    QtShapePolySet hull;
    
    // Add pads
    for (const EDA_PAD_DATA* pad : m_pads) {
        // Simplified - add pad outline to hull
        QRectF padBbox = pad->getBoundingBox();
        QPolygonF padPoly;
        padPoly << padBbox.topLeft() << padBbox.topRight() 
                << padBbox.bottomRight() << padBbox.bottomLeft();
        hull.addPolygon(padPoly);
    }
    
    // Add graphical items
    for (const EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        QRectF itemBbox = item->getBoundingBox();
        QPolygonF itemPoly;
        itemPoly << itemBbox.topLeft() << itemBbox.topRight() 
                 << itemBbox.bottomRight() << itemBbox.bottomLeft();
        hull.addPolygon(itemPoly);
    }
    
    // Compute convex hull
    hull.simplify();
    
    m_cachedHull = hull;
    m_hullCacheTimeStamp = getBoard()->getTimeStamp();
    
    return hull;
}

bool EDA_FOOTPRINT_DATA::textOnly() const
{
    // Check if footprint contains only text elements
    for (const EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        if (item->getType() != PCB_TEXT_T && item->getType() != PCB_FIELD_T)
            return false;
    }
    
    return m_pads.isEmpty() && m_zones.isEmpty();
}

// Transformation operations
void EDA_FOOTPRINT_DATA::move(const QPointF& aMoveVector)
{
    setPosition(m_pos + aMoveVector);
}

void EDA_FOOTPRINT_DATA::rotate(const QPointF& aRotCentre, const QtEdaAngle& aAngle)
{
    if (aAngle.value == 0)
        return;
    
    // Rotate position
    QPointF newPos = m_pos - aRotCentre;
    double angle_rad = aAngle.value * M_PI / 180.0;
    double cos_angle = cos(angle_rad);
    double sin_angle = sin(angle_rad);
    
    double new_x = newPos.x() * cos_angle - newPos.y() * sin_angle;
    double new_y = newPos.x() * sin_angle + newPos.y() * cos_angle;
    
    m_pos = QPointF(new_x, new_y) + aRotCentre;
    
    // Update orientation
    setOrientation(QtEdaAngle(m_orient.value + aAngle.value));
}

void EDA_FOOTPRINT_DATA::flip(const QPointF& aCentre, QtFlipDirection aFlipDirection)
{
    // Flip position
    if (aFlipDirection == QtFlipDirection::LeftRight)
        m_pos.setX(2 * aCentre.x() - m_pos.x());
    else
        m_pos.setY(2 * aCentre.y() - m_pos.y());

    // Flip layer
    if (getLayer() == QtPcbLayerId::FCu)
        setLayer(QtPcbLayerId::BCu);
    else if (getLayer() == QtPcbLayerId::BCu)
        setLayer(QtPcbLayerId::FCu);

    // Flip orientation
    if (aFlipDirection == QtFlipDirection::LeftRight)
        setOrientation(QtEdaAngle(-m_orient.value));
    
    // Flip all child items
    for (EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field)
            field->flip(m_pos, aFlipDirection);
    }
    
    for (EDA_PAD_DATA* pad : m_pads)
        pad->flip(m_pos, aFlipDirection);
    
    for (EDA_BOARD_OBJECT_DATA* item : m_drawings)
        item->flip(m_pos, aFlipDirection);
    
    for (EDA_ZONE_DATA* zone : m_zones)
        zone->flip(m_pos, aFlipDirection);
    
    for (QtPcbGroup* group : m_groups)
        group->flip(m_pos, aFlipDirection);
    
    // Flip 3D models
    for (QtFp3DModel& model : m_3DDrawings) {
        model.m_rotation.y = -model.m_rotation.y;
        model.m_rotation.z = -model.m_rotation.z;
        model.m_offset.x = -model.m_offset.x;
    }
    
    m_boundingBoxCacheTimeStamp = 0;
    m_textExcludedBBoxCacheTimeStamp = 0;
    m_hullCacheTimeStamp = 0;
}

void EDA_FOOTPRINT_DATA::moveAnchorPosition(const QPointF& aMoveVector)
{
    // Move all items relative to footprint origin
    for (EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field)
            field->move(-aMoveVector);
    }
    
    for (EDA_PAD_DATA* pad : m_pads)
        pad->move(-aMoveVector);
    
    for (EDA_BOARD_OBJECT_DATA* item : m_drawings)
        item->move(-aMoveVector);
    
    for (EDA_ZONE_DATA* zone : m_zones)
        zone->move(-aMoveVector);
    
    for (QtPcbGroup* group : m_groups)
        group->move(-aMoveVector);
    
    // Move 3D model offsets
    for (QtFp3DModel& model : m_3DDrawings) {
        model.m_offset.x -= aMoveVector.x() / 1000000.0; // Convert to mm
        model.m_offset.y -= aMoveVector.y() / 1000000.0;
    }
    
    m_boundingBoxCacheTimeStamp = 0;
    m_textExcludedBBoxCacheTimeStamp = 0;
    m_hullCacheTimeStamp = 0;
}

// Hit testing
bool EDA_FOOTPRINT_DATA::hitTest(const QPointF& aPosition, int aAccuracy) const
{
    QRectF bbox = getBoundingBox();
    return bbox.adjusted(-aAccuracy, -aAccuracy, aAccuracy, aAccuracy).contains(aPosition);
}

bool EDA_FOOTPRINT_DATA::hitTestAccurate(const QPointF& aPosition, int aAccuracy) const
{
    // Check bounding polygon
    QtShapePolySet hull = getBoundingHull();
    return hull.collide(aPosition, aAccuracy);
}

bool EDA_FOOTPRINT_DATA::hitTest(const QRectF& aRect, bool aContained, int aAccuracy) const
{
    QRectF bbox = getBoundingBox();
    QRectF rect = aRect.normalized();
    
    if (aAccuracy != 0)
        rect.adjust(-aAccuracy, -aAccuracy, aAccuracy, aAccuracy);
    
    if (aContained)
        return rect.contains(bbox);
    else
        return rect.intersects(bbox);
}

bool EDA_FOOTPRINT_DATA::hitTestOnLayer(const QPointF& aPosition, QtPcbLayerId aLayer, int aAccuracy) const
{
    // Check pads
    for (const EDA_PAD_DATA* pad : m_pads) {
        if (pad->isOnLayer(aLayer) && pad->hitTest(aPosition, aAccuracy))
            return true;
    }
    
    // Check graphical items
    for (const EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        if (item->isOnLayer(aLayer) && item->hitTest(aPosition, aAccuracy))
            return true;
    }
    
    // Check zones
    for (const EDA_ZONE_DATA* zone : m_zones) {
        if (zone->isOnLayer(aLayer) && zone->hitTest(aPosition, aAccuracy))
            return true;
    }
    
    return false;
}

bool EDA_FOOTPRINT_DATA::hitTestOnLayer(const QRectF& aRect, bool aContained, QtPcbLayerId aLayer, int aAccuracy) const
{
    // Similar implementation for rectangle hit test
    for (const EDA_PAD_DATA* pad : m_pads) {
        if (pad->isOnLayer(aLayer) && pad->hitTest(aRect, aContained, aAccuracy))
            return true;
    }
    
    for (const EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        if (item->isOnLayer(aLayer) && item->hitTest(aRect, aContained, aAccuracy))
            return true;
    }
    
    for (const EDA_ZONE_DATA* zone : m_zones) {
        if (zone->isOnLayer(aLayer) && zone->hitTest(aRect, aContained, aAccuracy))
            return true;
    }
    
    return false;
}

// Field positioning
void EDA_FOOTPRINT_DATA::autoPositionFields()
{
    // Position reference at top and value at bottom of bounding box
    QRectF bbox = getBoundingBox(false);
    
    if (bbox.isValid()) {
        // Position reference field above footprint
        if (EDA_BRD_FIELD_DATA* ref = getField(0)) {
            ref->setPosition(QPointF(bbox.center().x(), bbox.top() - 1000000)); // 1mm above
        }
        
        // Position value field below footprint
        if (EDA_BRD_FIELD_DATA* val = getField(1)) {
            val->setPosition(QPointF(bbox.center().x(), bbox.bottom() + 1000000)); // 1mm below
        }
    }
}

void EDA_FOOTPRINT_DATA::incrementReference(int aDelta)
{
    QString ref = getReference();
    
    // Extract numeric suffix
    int pos = ref.length() - 1;
    while (pos >= 0 && ref[pos].isDigit())
        pos--;
    
    if (pos < ref.length() - 1) {
        QString prefix = ref.left(pos + 1);
        QString numberStr = ref.mid(pos + 1);
        bool ok;
        int number = numberStr.toInt(&ok);
        
        if (ok) {
            number += aDelta;
            setReference(prefix + QString::number(number));
        }
    }
}

// Type information
QString EDA_FOOTPRINT_DATA::getTypeName() const
{
    if (m_attributes & FP_THROUGH_HOLE)
        return "Through hole";
    else if (m_attributes & FP_SMD)
        return "SMD";
    else
        return "Other";
}

int EDA_FOOTPRINT_DATA::getLikelyAttribute() const
{
    int smdCount = 0;
    int thtCount = 0;
    
    for (const EDA_PAD_DATA* pad : m_pads) {
        switch (pad->getAttribute()) {
        case QtPadAttribute::PTH:
        case QtPadAttribute::NPTH:
            thtCount++;
            break;
        case QtPadAttribute::SMD:
            smdCount++;
            break;
        }
    }
    
    if (thtCount > 0)
        return FP_THROUGH_HOLE;
    else if (smdCount > 0)
        return FP_SMD;
    else
        return 0;
}

double EDA_FOOTPRINT_DATA::getArea(int aPadding) const
{
    QRectF bbox = getBoundingBox();
    
    if (aPadding > 0)
        bbox.adjust(-aPadding, -aPadding, aPadding, aPadding);
    
    return bbox.width() * bbox.height();
}

// Duplication
EDA_BOARD_OBJECT_DATA* EDA_FOOTPRINT_DATA::duplicate() const
{
    return new EDA_FOOTPRINT_DATA(*this);
}

EDA_BOARD_OBJECT_DATA* EDA_FOOTPRINT_DATA::duplicateItem(const EDA_BOARD_OBJECT_DATA* aItem, bool aAddToFootprint)
{
    EDA_BOARD_OBJECT_DATA* newItem = nullptr;
    
    switch (aItem->getType()) {
    case PCB_PAD_T:
        newItem = new EDA_PAD_DATA(*static_cast<const EDA_PAD_DATA*>(aItem));
        break;
    case PCB_ZONE_T:
        newItem = new EDA_ZONE_DATA(*static_cast<const EDA_ZONE_DATA*>(aItem));
        break;
    case PCB_FIELD_T:
        newItem = new EDA_BRD_FIELD_DATA(*static_cast<const EDA_BRD_FIELD_DATA*>(aItem));
        break;
    case PCB_GROUP_T:
        newItem = new QtPcbGroup(*static_cast<const QtPcbGroup*>(aItem));
        break;
    default:
        newItem = static_cast<EDA_BOARD_OBJECT_DATA*>(aItem->clone());
        break;
    }
    
    if (newItem && aAddToFootprint)
        add(newItem);
    
    return newItem;
}

void EDA_FOOTPRINT_DATA::add3DModel(QtFp3DModel* a3DModel)
{
    if (a3DModel)
        m_3DDrawings.append(*a3DModel);
    // Models changed notification removed (Qt object system removal)
}

// Validation stubs
void EDA_FOOTPRINT_DATA::checkFootprintAttributes(const std::function<void(const QString&)>& aErrorHandler)
{
    // Placeholder implementation
}

void EDA_FOOTPRINT_DATA::checkPads(QtUnitsProvider* aUnitsProvider,
                           const std::function<void(const EDA_PAD_DATA*, int, const QString&)>& aErrorHandler)
{
    // Placeholder implementation
}

void EDA_FOOTPRINT_DATA::checkShortingPads(const std::function<void(const EDA_PAD_DATA*, const EDA_PAD_DATA*, int,
                                                           const QPointF&)>& aErrorHandler)
{
    // Placeholder implementation
}

void EDA_FOOTPRINT_DATA::checkNetTies(const std::function<void(const EDA_BOARD_OBJECT_DATA*, const EDA_BOARD_OBJECT_DATA*,
                                                      const EDA_BOARD_OBJECT_DATA*, const QPointF&)>& aErrorHandler)
{
    // Placeholder implementation
}

void EDA_FOOTPRINT_DATA::checkNetTiePadGroups(const std::function<void(const QString&)>& aErrorHandler)
{
    // Placeholder implementation
}

// Library validation
bool EDA_FOOTPRINT_DATA::isLibNameValid(const QString& aName)
{
    if (aName.isEmpty())
        return false;
    
    // Check for invalid characters
    const QString invalidChars = "/\\:*?\"<>|";
    for (QChar c : aName) {
        if (invalidChars.contains(c))
            return false;
    }
    
    return true;
}

const QChar* EDA_FOOTPRINT_DATA::stringLibNameInvalidChars(bool aUserReadable)
{
    static const QChar invalidChars[] = {'/', '\\', ':', '*', '?', '"', '<', '>', '|', '\0'};
    static const QChar readableInvalidChars[] = {'/', '\\', ':', '*', '?', '"', '<', '>', '|', ' ', '\t', '\0'};
    
    return aUserReadable ? readableInvalidChars : invalidChars;
}

// Comments
void EDA_FOOTPRINT_DATA::setInitialComments(QStringList* aInitialComments)
{
    delete m_initialComments;
    m_initialComments = aInitialComments;
}

// Shape generation stubs
std::shared_ptr<QtShape> EDA_FOOTPRINT_DATA::getEffectiveShape(QtPcbLayerId aLayer, int aFlash) const
{
    // Placeholder - return bounding box shape
    return std::shared_ptr<QtShape>();
}

void EDA_FOOTPRINT_DATA::transformPadsToPolySet(QtShapePolySet& aBuffer, QtPcbLayerId aLayer, int aClearance,
                                        int aMaxError, QtErrorLoc aErrorLoc,
                                        bool aSkipNPTHPadsWihNoCopper,
                                        bool aSkipPlatedPads,
                                        bool aSkipNonPlatedPads) const
{
    // Placeholder implementation
}

void EDA_FOOTPRINT_DATA::transformFPShapesToPolySet(QtShapePolySet& aBuffer, QtPcbLayerId aLayer, int aClearance,
                                            int aError, QtErrorLoc aErrorLoc,
                                            bool aIncludeText,
                                            bool aIncludeShapes,
                                            bool aIncludePrivateItems) const
{
    // Placeholder implementation
}

void EDA_FOOTPRINT_DATA::transformFPTextToPolySet(QtShapePolySet& aBuffer, QtPcbLayerId aLayer, int aClearance,
                                          int aError, QtErrorLoc aErrorLoc) const
{
    transformFPShapesToPolySet(aBuffer, aLayer, aClearance, aError, aErrorLoc, true, false);
}

// Text variables
void EDA_FOOTPRINT_DATA::getContextualTextVars(QStringList* aVars) const
{
    aVars->append("REFERENCE");
    aVars->append("VALUE");
    aVars->append("LAYER");
    aVars->append("FOOTPRINT_LIBRARY");
    aVars->append("FOOTPRINT_NAME");
    aVars->append("SHORT_NET_NAME(<pad_number>)");
    aVars->append("NET_NAME(<pad_number>)");
    aVars->append("NET_CLASS(<pad_number>)");
    aVars->append("PIN_NAME(<pad_number>)");
}

bool EDA_FOOTPRINT_DATA::resolveTextVar(QString* token, int aDepth) const
{
    if (*token == "REFERENCE") {
        *token = getReference();
        return true;
    }
    else if (*token == "VALUE") {
        *token = getValue();
        return true;
    }
    else if (*token == "LAYER") {
        *token = getLayerName();
        return true;
    }
    else if (*token == "FOOTPRINT_LIBRARY") {
        *token = m_fpid.getUniStringLibNickname();
        return true;
    }
    else if (*token == "FOOTPRINT_NAME") {
        *token = m_fpid.getUniStringLibItemName();
        return true;
    }
    else if (token->startsWith("SHORT_NET_NAME(") ||
             token->startsWith("NET_NAME(") ||
             token->startsWith("NET_CLASS(") ||
             token->startsWith("PIN_NAME(")) {
        int start = token->indexOf('(');
        int end = token->lastIndexOf(')');
        if (start != -1 && end != -1 && end > start) {
            QString padNumber = token->mid(start + 1, end - start - 1);
            
            for (EDA_PAD_DATA* pad : m_pads) {
                if (pad->getNumber() == padNumber) {
                    if (token->startsWith("SHORT_NET_NAME"))
                        *token = pad->getShortNetName();
                    else if (token->startsWith("NET_NAME"))
                        *token = pad->getNetName();
                    else if (token->startsWith("NET_CLASS"))
                        *token = pad->getNetClassName();
                    else
                        *token = pad->getPinFunction();
                    
                    return true;
                }
            }
        }
    }
    else if (hasFieldByName(*token)) {
        *token = getFieldText(*token);
        return true;
    }
    
    return false;
}

// Courtyard management stubs
const QtShapePolySet& EDA_FOOTPRINT_DATA::getCourtyard(QtPcbLayerId aLayer) const
{
    if (aLayer == QtPcbLayerId::FCrtYd)
        return m_courtyardCacheFront;
    else
        return m_courtyardCacheBack;
}

const QtShapePolySet& EDA_FOOTPRINT_DATA::getCachedCourtyard(QtPcbLayerId aLayer) const
{
    return getCourtyard(aLayer);
}

void EDA_FOOTPRINT_DATA::buildCourtyardCaches(QtOutlineErrorHandler* aErrorHandler)
{
    // Placeholder implementation
    QMutexLocker locker(&m_courtyardCacheMutex);

    m_courtyardCacheFront.clear();
    m_courtyardCacheBack.clear();
    
    // Build courtyard from graphical items on courtyard layers
    for (EDA_BOARD_OBJECT_DATA* item : m_drawings) {
        if (item->getLayer() == QtPcbLayerId::FCrtYd) {
            // Add to front courtyard
        }
        else if (item->getLayer() == QtPcbLayerId::BCrtYd) {
            // Add to back courtyard
        }
    }
}

// Component class
QString EDA_FOOTPRINT_DATA::getComponentClassAsString() const
{
    if (m_componentClass)
        return "ComponentClass"; // Placeholder
    return QString();
}

void EDA_FOOTPRINT_DATA::resolveComponentClassNames(EDA_BOARD_DATA* aBoard, const QSet<QString>& aComponentClassNames)
{
    // Placeholder implementation
}

// Embedded files stubs
QtEmbeddedFiles* EDA_FOOTPRINT_DATA::getEmbeddedFiles()
{
    return nullptr; // Placeholder
}

const QtEmbeddedFiles* EDA_FOOTPRINT_DATA::getEmbeddedFiles() const
{
    return nullptr; // Placeholder
}

QSet<QtOutlineFont*> EDA_FOOTPRINT_DATA::getFonts() const
{
    return QSet<QtOutlineFont*>(); // Placeholder
}

void EDA_FOOTPRINT_DATA::embedFonts()
{
    // Placeholder implementation
}

// Comparison
double EDA_FOOTPRINT_DATA::similarity(const EDA_BOARD_OBJECT_DATA& aOther) const
{
    if (aOther.getType() != PCB_FOOTPRINT_T)
        return 0.0;
    
    const EDA_FOOTPRINT_DATA& other = static_cast<const EDA_FOOTPRINT_DATA&>(aOther);
    
    double similarity = 1.0;
    
    // Compare basic properties
    if (m_fpid.format() != other.m_fpid.format())
        similarity *= 0.9;
    
    if (m_pads.size() != other.m_pads.size())
        similarity *= 0.8;
    
    if (m_drawings.size() != other.m_drawings.size())
        similarity *= 0.9;
    
    return similarity;
}

bool EDA_FOOTPRINT_DATA::operator==(const EDA_BOARD_OBJECT_DATA& aOther) const
{
    if (aOther.getType() != PCB_FOOTPRINT_T)
        return false;
    
    return operator==(static_cast<const EDA_FOOTPRINT_DATA&>(aOther));
}

bool EDA_FOOTPRINT_DATA::operator==(const EDA_FOOTPRINT_DATA& aOther) const
{
    // Compare all relevant properties
    if (m_fpid.format() != aOther.m_fpid.format())
        return false;
    
    if (m_pos != aOther.m_pos)
        return false;
    
    if (m_orient.value != aOther.m_orient.value)
        return false;
    
    if (m_attributes != aOther.m_attributes)
        return false;
    
    if (m_pads.size() != aOther.m_pads.size())
        return false;
    
    if (m_drawings.size() != aOther.m_drawings.size())
        return false;
    
    if (m_zones.size() != aOther.m_zones.size())
        return false;
    
    // Would need to compare all child items for full equality
    
    return true;
}

bool EDA_FOOTPRINT_DATA::footprintNeedsUpdate(const EDA_FOOTPRINT_DATA* aLibFP, int aCompareFlags,
                                      QtReporter* aReporter)
{
    // Placeholder - always return false
    return false;
}

// Coverage calculation stubs
double EDA_FOOTPRINT_DATA::coverageRatio(const QtGeneralCollector& aCollector) const
{
    // Placeholder
    return 0.5;
}

double EDA_FOOTPRINT_DATA::getCoverageArea(const EDA_BOARD_OBJECT_DATA* aItem, const QtGeneralCollector& aCollector)
{
    // Placeholder
    return 0.0;
}

// Visitor pattern
void EDA_FOOTPRINT_DATA::runOnChildren(const std::function<void(EDA_BOARD_OBJECT_DATA*)>& aFunction) const
{
    for (EDA_BRD_FIELD_DATA* field : m_fields) {
        if (field)
            aFunction(field);
    }
    
    for (EDA_PAD_DATA* pad : m_pads)
        aFunction(pad);
    
    for (EDA_BOARD_OBJECT_DATA* item : m_drawings)
        aFunction(item);
    
    for (EDA_ZONE_DATA* zone : m_zones)
        aFunction(zone);
    
    for (QtPcbGroup* group : m_groups)
        aFunction(group);
}

void EDA_FOOTPRINT_DATA::runOnDescendants(const std::function<void(EDA_BOARD_OBJECT_DATA*)>& aFunction, int aDepth) const
{
    runOnChildren(aFunction);
    
    // Recurse into groups
    for (QtPcbGroup* group : m_groups) {
        for (EDA_BOARD_OBJECT_DATA* item : group->getItems())
            aFunction(item);
    }
}

// Descriptive information
QString EDA_FOOTPRINT_DATA::getItemDescription(QtUnitsProvider* aUnitsProvider, bool aFull) const
{
    if (aFull) {
        return QString("Footprint %1 (%2)").arg(getReference()).arg(getValue());
    } else {
        return QString("Footprint %1").arg(getReference());
    }
}

// Cloning
EDA_BOARD_OBJECT_DATA* EDA_FOOTPRINT_DATA::clone() const
{
    return new EDA_FOOTPRINT_DATA(*this);
}

// Protected methods
void EDA_FOOTPRINT_DATA::swapData(EDA_BOARD_OBJECT_DATA* aImage)
{
    EDA_FOOTPRINT_DATA* image = static_cast<EDA_FOOTPRINT_DATA*>(aImage);
    
    std::swap(*this, *image);
}

//=============================================================================
// PURE VIRTUAL METHOD IMPLEMENTATIONS
//=============================================================================

double EDA_FOOTPRINT_DATA::similarity(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return 0.0;
    }
    
    const EDA_FOOTPRINT_DATA* footprint = dynamic_cast<const EDA_FOOTPRINT_DATA*>(&other);
    if (!footprint) {
        return 0.0;
    }
    
    return operator==(*footprint) ? 1.0 : 0.0;
}

bool EDA_FOOTPRINT_DATA::operator==(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return false;
    }
    
    const EDA_FOOTPRINT_DATA* footprint = dynamic_cast<const EDA_FOOTPRINT_DATA*>(&other);
    if (!footprint) {
        return false;
    }
    
    return operator==(*footprint);
}

bool EDA_FOOTPRINT_DATA::operator==(const EDA_FOOTPRINT_DATA& other) const
{
    // Compare essential footprint properties
    return m_fpid == other.m_fpid &&
           m_pos == other.m_pos &&
           m_orient == other.m_orient &&
           m_attributes == other.m_attributes &&
           getReference() == other.getReference() &&
           getValue() == other.getValue();
}

// Comparator implementations
bool QtCmpDrawings::operator()(const EDA_BOARD_OBJECT_DATA* itemA, const EDA_BOARD_OBJECT_DATA* itemB) const
{
    if (itemA->getType() != itemB->getType())
        return static_cast<int>(itemA->getType()) < static_cast<int>(itemB->getType());
    
    if (itemA->getLayer() != itemB->getLayer())
        return static_cast<int>(itemA->getLayer()) < static_cast<int>(itemB->getLayer());
    
    return itemA->getUuid().toString() < itemB->getUuid().toString();
}

bool QtCmpPads::operator()(const EDA_PAD_DATA* aFirst, const EDA_PAD_DATA* aSecond) const
{
    if (aFirst->getNumber() != aSecond->getNumber())
        return aFirst->getNumber() < aSecond->getNumber();

    return aFirst->getUuid().toString() < aSecond->getUuid().toString();
}

bool QtCmpZones::operator()(const EDA_ZONE_DATA* aFirst, const EDA_ZONE_DATA* aSecond) const
{
    if (aFirst->getLayer() != aSecond->getLayer())
        return static_cast<int>(aFirst->getLayer()) < static_cast<int>(aSecond->getLayer());
    
    if (aFirst->getAssignedPriority() != aSecond->getAssignedPriority())
        return aFirst->getAssignedPriority() > aSecond->getAssignedPriority();
    
    return aFirst->getUuid().toString() < aSecond->getUuid().toString();
}

//=============================================================================
// CONTAINER INTERFACE IMPLEMENTATIONS
//=============================================================================

void EDA_FOOTPRINT_DATA::addItem(EDA_BOARD_OBJECT_DATA* item, QtAddMode mode, bool skipConnectivity)
{
    add(item, mode, skipConnectivity);
}

void EDA_FOOTPRINT_DATA::removeItem(EDA_BOARD_OBJECT_DATA* item, QtRemoveMode mode)
{
    remove(item, mode);
}

int EDA_FOOTPRINT_DATA::getItemCount() const
{
    return m_pads.size() + m_drawings.size() + m_zones.size() + m_groups.size();
}

bool EDA_FOOTPRINT_DATA::isEmpty() const
{
    return m_pads.isEmpty() && m_drawings.isEmpty() && m_zones.isEmpty() && m_groups.isEmpty();
}

bool EDA_FOOTPRINT_DATA::contains(const EDA_BOARD_OBJECT_DATA* item) const
{
    if (!item) return false;
    
    // Check pads
    for (const auto* pad : m_pads) {
        if (pad == item) return true;
    }
    
    // Check drawings
    for (const auto* drawing : m_drawings) {
        if (drawing == item) return true;
    }
    
    // Check zones
    for (const auto* zone : m_zones) {
        if (zone == item) return true;
    }
    
    // Check groups
    for (const auto* group : m_groups) {
        if (group == item) return true;
    }
    
    return false;
}

QList<EDA_BOARD_OBJECT_DATA*> EDA_FOOTPRINT_DATA::getAllItems() const
{
    QList<EDA_BOARD_OBJECT_DATA*> items;
    
    // Add all pads
    for (auto* pad : m_pads) {
        items.append(pad);
    }
    
    // Add all drawings
    for (auto* drawing : m_drawings) {
        items.append(drawing);
    }
    
    // Add all zones
    for (auto* zone : m_zones) {
        items.append(zone);
    }
    
    // Add all groups
    for (auto* group : m_groups) {
        items.append(group);
    }
    
    return items;
}

QList<EDA_BOARD_OBJECT_DATA*> EDA_FOOTPRINT_DATA::getItemsByType(QtKicadType type) const
{
    QList<EDA_BOARD_OBJECT_DATA*> items;
    
    // Check all items and filter by type
    for (auto* item : getAllItems()) {
        if (item && item->getType() == type) {
            items.append(item);
        }
    }
    
    return items;
}

void EDA_FOOTPRINT_DATA::clear()
{
    // Clear all collections
    m_pads.clear();
    m_drawings.clear();
    m_zones.clear();
    m_groups.clear();
}